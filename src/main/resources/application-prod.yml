spring:
  application:
    name: Chollisimos

  data:
    mongodb:
      uri: ${SPRING_DATA_MONGODB_URI:mongodb://mongodb-service:27017/chollisimos_production}
  
  thymeleaf:
    cache: true
    
server:
  port: 8080

logging:
  level:
    com.adrianheras.chollisimos: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate: WARN
  file:
    name: /app/logs/chollisimos.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
